import os
import json
import hashlib
from typing import List, Dict, Any, Optional, Tuple
from abc import ABC, abstractmethod
import numpy as np
import faiss
import chromadb
from chromadb.config import Settings
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import OpenAIEmbeddings
try:
    from sentence_transformers import SentenceTransformer
    SENTENCE_TRANSFORMERS_AVAILABLE = True
except ImportError:
    SENTENCE_TRANSFORMERS_AVAILABLE = False

from ..core.config import settings
from ..core.database import get_db
from ..models.knowledge_base import KnowledgeChunk
from sqlalchemy.orm import Session


class BaseVectorStore(ABC):
    """向量数据库基类"""
    
    @abstractmethod
    def add_documents(self, texts: List[str], metadatas: List[Dict], ids: List[str]) -> bool:
        """添加文档"""
        pass
    
    @abstractmethod
    def search(self, query: str, k: int = 5) -> List[Dict]:
        """搜索相似文档"""
        pass
    
    @abstractmethod
    def delete_documents(self, ids: List[str]) -> bool:
        """删除文档"""
        pass
    
    @abstractmethod
    def update_documents(self, ids: List[str], texts: List[str], metadatas: List[Dict]) -> bool:
        """更新文档"""
        pass


class FAISSVectorStore(BaseVectorStore):
    """FAISS向量数据库实现"""
    
    def __init__(self, knowledge_base_id: int, embedding_model: str = None):
        self.knowledge_base_id = knowledge_base_id
        self.embedding_model = embedding_model or settings.EMBEDDING_MODEL
        self.dimension = settings.VECTOR_DIMENSION
        
        # 初始化embedding模型
        # 检查是否为OpenAI模型
        openai_models = ["text-embedding-ada-002", "text-embedding-3-small", "text-embedding-3-large"]
        if (any(model in self.embedding_model.lower() for model in openai_models) or
            "openai" in self.embedding_model.lower() or
            "ada" in self.embedding_model.lower()):
            # 使用OpenAI embeddings
            self.embeddings = OpenAIEmbeddings(
                openai_api_key=settings.OPENAI_API_KEY,
                openai_api_base=settings.OPENAI_BASE_URL,
                model=self.embedding_model
            )
            self.use_openai = True
            print(f"使用OpenAI embedding模型: {self.embedding_model}")
        else:
            # 使用sentence transformers
            if not SENTENCE_TRANSFORMERS_AVAILABLE:
                raise ImportError("sentence-transformers not available. Please install it or use OpenAI embeddings.")
            self.embeddings = SentenceTransformer(self.embedding_model)
            self.use_openai = False
            print(f"使用Sentence Transformers模型: {self.embedding_model}")
        
        # 初始化FAISS索引
        self.index_path = os.path.join(
            settings.VECTOR_STORE_PATH, 
            f"faiss_kb_{knowledge_base_id}"
        )
        os.makedirs(self.index_path, exist_ok=True)
        
        self.index_file = os.path.join(self.index_path, "index.faiss")
        self.metadata_file = os.path.join(self.index_path, "metadata.json")
        
        self._load_or_create_index()
    
    def _load_or_create_index(self):
        """加载或创建FAISS索引"""
        if os.path.exists(self.index_file):
            self.index = faiss.read_index(self.index_file)
            with open(self.metadata_file, 'r', encoding='utf-8') as f:
                self.metadata = json.load(f)
        else:
            self.index = faiss.IndexFlatIP(self.dimension)  # 内积索引
            self.metadata = {}
    
    def _save_index(self):
        """保存索引和元数据"""
        faiss.write_index(self.index, self.index_file)
        with open(self.metadata_file, 'w', encoding='utf-8') as f:
            json.dump(self.metadata, f, ensure_ascii=False, indent=2)
    
    def _get_embeddings(self, texts: List[str]) -> np.ndarray:
        """获取文本嵌入向量"""
        try:
            if self.use_openai:
                # OpenAI embeddings
                embeddings = self.embeddings.embed_documents(texts)
                return np.array(embeddings, dtype=np.float32)
            else:
                # Sentence transformers
                embeddings = self.embeddings.encode(texts)
                return embeddings.astype(np.float32)
        except Exception as e:
            print(f"获取embeddings失败: {e}")
            raise
    
    def add_documents(self, texts: List[str], metadatas: List[Dict], ids: List[str]) -> bool:
        """添加文档到向量数据库"""
        try:
            # 获取嵌入向量
            embeddings = self._get_embeddings(texts)
            
            # 标准化向量（用于余弦相似度）
            faiss.normalize_L2(embeddings)
            
            # 添加到索引
            self.index.add(embeddings)
            
            # 保存元数据
            start_id = self.index.ntotal - len(embeddings)
            for i, (text, metadata, doc_id) in enumerate(zip(texts, metadatas, ids)):
                self.metadata[str(start_id + i)] = {
                    'id': doc_id,
                    'text': text,
                    'metadata': metadata
                }
            
            self._save_index()
            return True
        except Exception as e:
            print(f"添加文档到FAISS失败: {e}")
            return False
    
    def search(self, query: str, k: int = 5) -> List[Dict]:
        """搜索相似文档"""
        try:
            if self.index.ntotal == 0:
                return []
            
            # 获取查询向量
            query_embedding = self._get_embeddings([query])
            faiss.normalize_L2(query_embedding)
            
            # 搜索
            scores, indices = self.index.search(query_embedding, min(k, self.index.ntotal))
            
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx != -1 and str(idx) in self.metadata:
                    result = self.metadata[str(idx)].copy()
                    result['similarity_score'] = float(score)
                    results.append(result)
            
            return results
        except Exception as e:
            print(f"FAISS搜索失败: {e}")
            return []
    
    def delete_documents(self, ids: List[str]) -> bool:
        """删除文档（FAISS不支持直接删除，需要重建索引）"""
        try:
            # 过滤要保留的文档
            remaining_metadata = {}
            remaining_texts = []
            remaining_ids = []
            
            for idx, meta in self.metadata.items():
                if meta['id'] not in ids:
                    remaining_metadata[len(remaining_texts)] = meta
                    remaining_texts.append(meta['text'])
                    remaining_ids.append(meta['id'])
            
            # 重建索引
            if remaining_texts:
                self.index = faiss.IndexFlatIP(self.dimension)
                embeddings = self._get_embeddings(remaining_texts)
                faiss.normalize_L2(embeddings)
                self.index.add(embeddings)
                self.metadata = remaining_metadata
            else:
                self.index = faiss.IndexFlatIP(self.dimension)
                self.metadata = {}
            
            self._save_index()
            return True
        except Exception as e:
            print(f"FAISS删除文档失败: {e}")
            return False
    
    def update_documents(self, ids: List[str], texts: List[str], metadatas: List[Dict]) -> bool:
        """更新文档"""
        # 先删除再添加
        self.delete_documents(ids)
        return self.add_documents(texts, metadatas, ids)


class ChromaVectorStore(BaseVectorStore):
    """ChromaDB向量数据库实现"""
    
    def __init__(self, knowledge_base_id: int, embedding_model: str = None):
        self.knowledge_base_id = knowledge_base_id
        self.embedding_model = embedding_model or settings.EMBEDDING_MODEL
        
        # 初始化ChromaDB
        self.chroma_path = os.path.join(settings.VECTOR_STORE_PATH, "chroma")
        os.makedirs(self.chroma_path, exist_ok=True)
        
        self.client = chromadb.PersistentClient(
            path=self.chroma_path,
            settings=Settings(anonymized_telemetry=False)
        )
        
        self.collection_name = f"kb_{knowledge_base_id}"
        self.collection = self.client.get_or_create_collection(
            name=self.collection_name,
            metadata={"knowledge_base_id": knowledge_base_id}
        )
    
    def add_documents(self, texts: List[str], metadatas: List[Dict], ids: List[str]) -> bool:
        """添加文档到ChromaDB"""
        try:
            self.collection.add(
                documents=texts,
                metadatas=metadatas,
                ids=ids
            )
            return True
        except Exception as e:
            print(f"添加文档到ChromaDB失败: {e}")
            return False
    
    def search(self, query: str, k: int = 5) -> List[Dict]:
        """搜索相似文档"""
        try:
            results = self.collection.query(
                query_texts=[query],
                n_results=k
            )
            
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    result = {
                        'id': results['ids'][0][i],
                        'text': doc,
                        'metadata': results['metadatas'][0][i] if results['metadatas'][0] else {},
                        'similarity_score': 1 - results['distances'][0][i]  # 转换为相似度
                    }
                    formatted_results.append(result)
            
            return formatted_results
        except Exception as e:
            print(f"ChromaDB搜索失败: {e}")
            return []
    
    def delete_documents(self, ids: List[str]) -> bool:
        """删除文档"""
        try:
            self.collection.delete(ids=ids)
            return True
        except Exception as e:
            print(f"ChromaDB删除文档失败: {e}")
            return False
    
    def update_documents(self, ids: List[str], texts: List[str], metadatas: List[Dict]) -> bool:
        """更新文档"""
        try:
            self.collection.update(
                ids=ids,
                documents=texts,
                metadatas=metadatas
            )
            return True
        except Exception as e:
            print(f"ChromaDB更新文档失败: {e}")
            return False


class VectorStoreManager:
    """向量数据库管理器"""
    
    def __init__(self):
        self.stores = {}
    
    def get_vector_store(self, knowledge_base_id: int, embedding_model: str = None) -> BaseVectorStore:
        """获取向量数据库实例"""
        store_key = f"{knowledge_base_id}_{embedding_model or settings.EMBEDDING_MODEL}"
        
        if store_key not in self.stores:
            if settings.VECTOR_STORE_TYPE.lower() == "faiss":
                self.stores[store_key] = FAISSVectorStore(knowledge_base_id, embedding_model)
            elif settings.VECTOR_STORE_TYPE.lower() == "chroma":
                self.stores[store_key] = ChromaVectorStore(knowledge_base_id, embedding_model)
            else:
                raise ValueError(f"不支持的向量数据库类型: {settings.VECTOR_STORE_TYPE}")
        
        return self.stores[store_key]
    
    def create_text_chunks(self, text: str, chunk_size: int = None, chunk_overlap: int = None) -> List[str]:
        """将文本分割成块"""
        chunk_size = chunk_size or settings.CHUNK_SIZE
        chunk_overlap = chunk_overlap or settings.CHUNK_OVERLAP
        
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            separators=["\n\n", "\n", " ", ""]
        )
        
        chunks = text_splitter.split_text(text)
        return chunks
    
    def add_document_to_knowledge_base(
        self, 
        knowledge_base_id: int, 
        document_id: int,
        text: str, 
        metadata: Dict = None,
        chunk_size: int = None,
        chunk_overlap: int = None
    ) -> bool:
        """将文档添加到知识库"""
        try:
            # 获取向量数据库
            vector_store = self.get_vector_store(knowledge_base_id)
            
            # 分割文本
            chunks = self.create_text_chunks(text, chunk_size, chunk_overlap)
            
            # 准备数据
            chunk_ids = []
            chunk_metadatas = []
            
            for i, chunk in enumerate(chunks):
                chunk_id = f"doc_{document_id}_chunk_{i}"
                chunk_metadata = {
                    "document_id": document_id,
                    "knowledge_base_id": knowledge_base_id,
                    "chunk_index": i,
                    **(metadata or {})
                }
                
                chunk_ids.append(chunk_id)
                chunk_metadatas.append(chunk_metadata)
            
            # 添加到向量数据库
            success = vector_store.add_documents(chunks, chunk_metadatas, chunk_ids)
            
            if success:
                # 保存到数据库
                db = next(get_db())
                try:
                    for i, (chunk, chunk_metadata) in enumerate(zip(chunks, chunk_metadatas)):
                        # 计算嵌入哈希用于去重
                        embedding_hash = hashlib.md5(chunk.encode()).hexdigest()
                        
                        db_chunk = KnowledgeChunk(
                            content=chunk,
                            metadata=json.dumps(chunk_metadata),
                            vector_id=chunk_ids[i],
                            embedding_hash=embedding_hash,
                            document_id=document_id,
                            knowledge_base_id=knowledge_base_id
                        )
                        db.add(db_chunk)
                    
                    db.commit()
                    return True
                except Exception as e:
                    db.rollback()
                    print(f"保存知识片段到数据库失败: {e}")
                    return False
                finally:
                    db.close()
            
            return success
        except Exception as e:
            print(f"添加文档到知识库失败: {e}")
            return False
    
    def search_knowledge_base(
        self, 
        knowledge_base_id: int, 
        query: str, 
        k: int = 5
    ) -> List[Dict]:
        """搜索知识库"""
        try:
            vector_store = self.get_vector_store(knowledge_base_id)
            return vector_store.search(query, k)
        except Exception as e:
            print(f"搜索知识库失败: {e}")
            return []
    
    def delete_document_from_knowledge_base(
        self, 
        knowledge_base_id: int, 
        document_id: int
    ) -> bool:
        """从知识库删除文档"""
        try:
            # 从数据库获取chunk IDs
            db = next(get_db())
            try:
                chunks = db.query(KnowledgeChunk).filter(
                    KnowledgeChunk.document_id == document_id,
                    KnowledgeChunk.knowledge_base_id == knowledge_base_id
                ).all()
                
                chunk_ids = [chunk.vector_id for chunk in chunks]
                
                if chunk_ids:
                    # 从向量数据库删除
                    vector_store = self.get_vector_store(knowledge_base_id)
                    vector_store.delete_documents(chunk_ids)
                    
                    # 从数据库删除
                    for chunk in chunks:
                        db.delete(chunk)
                    db.commit()
                
                return True
            except Exception as e:
                db.rollback()
                print(f"从知识库删除文档失败: {e}")
                return False
            finally:
                db.close()
        except Exception as e:
            print(f"删除文档失败: {e}")
            return False


# 全局向量数据库管理器实例
vector_store_manager = VectorStoreManager()
