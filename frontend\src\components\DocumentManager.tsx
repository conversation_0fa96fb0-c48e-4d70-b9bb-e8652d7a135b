import React, { useState, useEffect } from 'react'
import { 
  Modal, Table, Button, Upload, message, Space, Tag, Progress,
  Popconfirm, Input, Form, Typography, Divider
} from 'antd'
import { 
  UploadOutlined, DeleteOutlined, FileTextOutlined, 
  InboxOutlined, EditOutlined
} from '@ant-design/icons'
import { knowledgeService } from '../services/knowledgeService'

const { Dragger } = Upload
const { TextArea } = Input
const { Text } = Typography

interface Document {
  id: number
  filename: string
  original_filename: string
  file_size: number
  file_type: string
  title?: string
  summary?: string
  status: string
  total_chunks: number
  knowledge_base_id: number
  created_at: string
  processed_at?: string
}

interface DocumentManagerProps {
  visible: boolean
  onClose: () => void
  knowledgeBaseId: number
  knowledgeBaseName: string
}

const DocumentManager: React.FC<DocumentManagerProps> = ({
  visible,
  onClose,
  knowledgeBaseId,
  knowledgeBaseName
}) => {
  const [documents, setDocuments] = useState<Document[]>([])
  const [loading, setLoading] = useState(false)
  const [uploadMode, setUploadMode] = useState<'file' | 'text'>('file')
  const [textForm] = Form.useForm()

  useEffect(() => {
    if (visible && knowledgeBaseId) {
      loadDocuments()
    }
  }, [visible, knowledgeBaseId])

  const loadDocuments = async () => {
    try {
      setLoading(true)
      const docs = await knowledgeService.getKnowledgeBaseDocuments(knowledgeBaseId)
      setDocuments(docs)
    } catch (error) {
      console.error('加载文档失败:', error)
      message.error('加载文档失败')
    } finally {
      setLoading(false)
    }
  }

  const handleFileUpload = async (file: File) => {
    const formData = new FormData()
    formData.append('file', file)

    try {
      const token = localStorage.getItem('token')
      if (!token) {
        message.error('请先登录')
        return false
      }

      const response = await fetch(`http://localhost:8000/api/knowledge/${knowledgeBaseId}/documents`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      })

      if (response.ok) {
        message.success('文件上传成功，正在处理中...')
        loadDocuments()
        return true
      } else {
        const error = await response.json()
        message.error(`上传失败: ${error.detail || '未知错误'}`)
        return false
      }
    } catch (error) {
      console.error('上传失败:', error)
      message.error('上传失败')
      return false
    }
  }

  const handleTextUpload = async (values: any) => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        message.error('请先登录')
        return
      }

      const response = await fetch(`http://localhost:8000/api/knowledge/${knowledgeBaseId}/documents/text`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          filename: values.filename,
          content: values.content,
          knowledge_base_id: knowledgeBaseId
        })
      })

      if (response.ok) {
        message.success('文本上传成功，正在处理中...')
        textForm.resetFields()
        setUploadMode('file')
        loadDocuments()
      } else {
        const error = await response.json()
        message.error(`上传失败: ${error.detail || '未知错误'}`)
      }
    } catch (error) {
      console.error('上传失败:', error)
      message.error('上传失败')
    }
  }

  const handleDeleteDocument = async (docId: number) => {
    try {
      const token = localStorage.getItem('token')
      if (!token) {
        message.error('请先登录')
        return
      }

      const response = await fetch(`http://localhost:8000/api/knowledge/${knowledgeBaseId}/documents/${docId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        message.success('文档删除成功')
        loadDocuments()
      } else {
        const error = await response.json()
        message.error(`删除失败: ${error.detail || '未知错误'}`)
      }
    } catch (error) {
      console.error('删除失败:', error)
      message.error('删除失败')
    }
  }

  const getStatusTag = (status: string) => {
    const statusMap = {
      'pending': { color: 'orange', text: '等待处理' },
      'processing': { color: 'blue', text: '处理中' },
      'completed': { color: 'green', text: '已完成' },
      'failed': { color: 'red', text: '处理失败' }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const columns = [
    {
      title: '文档名称',
      dataIndex: 'original_filename',
      key: 'original_filename',
      render: (text: string, record: Document) => (
        <Space>
          <FileTextOutlined />
          <div>
            <div>{text}</div>
            {record.title && record.title !== text && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                标题: {record.title}
              </Text>
            )}
          </div>
        </Space>
      ),
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      render: (size: number) => formatFileSize(size),
    },
    {
      title: '文件类型',
      dataIndex: 'file_type',
      key: 'file_type',
      render: (type: string) => <Tag>{type.toUpperCase()}</Tag>,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
    },
    {
      title: '分块数量',
      dataIndex: 'total_chunks',
      key: 'total_chunks',
      render: (chunks: number) => chunks || 0,
    },
    {
      title: '上传时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record: Document) => (
        <Space>
          <Popconfirm
            title="确定要删除这个文档吗？"
            description="删除后将无法恢复，且会从向量数据库中移除。"
            onConfirm={() => handleDeleteDocument(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="link" 
              danger
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  const uploadProps = {
    name: 'file',
    multiple: true,
    beforeUpload: (file: File) => {
      handleFileUpload(file)
      return false // 阻止默认上传行为
    },
    showUploadList: false,
  }

  return (
    <Modal
      title={`文档管理 - ${knowledgeBaseName}`}
      open={visible}
      onCancel={onClose}
      width={1000}
      footer={null}
    >
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button 
            type={uploadMode === 'file' ? 'primary' : 'default'}
            icon={<UploadOutlined />}
            onClick={() => setUploadMode('file')}
          >
            文件上传
          </Button>
          <Button 
            type={uploadMode === 'text' ? 'primary' : 'default'}
            icon={<EditOutlined />}
            onClick={() => setUploadMode('text')}
          >
            文本上传
          </Button>
        </Space>
      </div>

      {uploadMode === 'file' ? (
        <Dragger {...uploadProps} style={{ marginBottom: 16 }}>
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持 PDF、Word、Excel、PowerPoint、文本文件等格式
          </p>
        </Dragger>
      ) : (
        <Form
          form={textForm}
          layout="vertical"
          onFinish={handleTextUpload}
          style={{ marginBottom: 16 }}
        >
          <Form.Item
            name="filename"
            label="文件名"
            rules={[{ required: true, message: '请输入文件名' }]}
          >
            <Input placeholder="例如：产品手册.txt" />
          </Form.Item>
          <Form.Item
            name="content"
            label="文本内容"
            rules={[{ required: true, message: '请输入文本内容' }]}
          >
            <TextArea 
              rows={8} 
              placeholder="请粘贴或输入文本内容..."
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                上传文本
              </Button>
              <Button onClick={() => textForm.resetFields()}>
                清空
              </Button>
            </Space>
          </Form.Item>
        </Form>
      )}

      <Divider />

      <Table
        columns={columns}
        dataSource={documents}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
        scroll={{ x: 800 }}
      />
    </Modal>
  )
}

export default DocumentManager
