from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
import os
import uuid

from ..core.database import get_db
from ..core.config import settings
from ..models.knowledge_base import KnowledgeBase
from ..models.document import Document
from ..models.app import App
from ..models.user import User
from .auth import get_current_user
from ..services.document_processor import document_processor
from ..services.vector_store import vector_store_manager

router = APIRouter()


class KnowledgeBaseCreate(BaseModel):
    name: str
    description: str = None
    app_id: int
    embedding_model: str = "text-embedding-ada-002"
    chunk_size: int = 1000
    chunk_overlap: int = 200


class DocumentResponse(BaseModel):
    id: int
    filename: str
    original_filename: str
    file_size: int
    file_type: str
    title: Optional[str] = None
    summary: Optional[str] = None
    status: str
    total_chunks: int
    knowledge_base_id: int
    created_at: datetime
    processed_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class DocumentUploadRequest(BaseModel):
    filename: str
    content: str
    knowledge_base_id: int


class KnowledgeBaseResponse(BaseModel):
    id: int
    name: str
    description: str = None
    app_id: int
    embedding_model: str
    chunk_size: int
    chunk_overlap: int
    is_active: bool
    total_chunks: int
    created_at: datetime

    class Config:
        from_attributes = True


@router.get("/", response_model=List[KnowledgeBaseResponse])
async def get_all_knowledge_bases(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取所有知识库列表"""
    knowledge_bases = db.query(KnowledgeBase).filter(
        KnowledgeBase.is_active == True
    ).all()

    return knowledge_bases


@router.post("/", response_model=KnowledgeBaseResponse)
async def create_knowledge_base(
    kb_data: KnowledgeBaseCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """创建知识库"""
    # 检查应用是否存在且用户有权限
    app = db.query(App).filter(App.id == kb_data.app_id).first()
    if not app:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    
    if app.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    db_kb = KnowledgeBase(
        name=kb_data.name,
        description=kb_data.description,
        app_id=kb_data.app_id,
        embedding_model=kb_data.embedding_model,
        chunk_size=kb_data.chunk_size,
        chunk_overlap=kb_data.chunk_overlap
    )
    
    db.add(db_kb)
    db.commit()
    db.refresh(db_kb)
    
    return db_kb


@router.get("/app/{app_id}", response_model=List[KnowledgeBaseResponse])
async def get_knowledge_bases_by_app(
    app_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取应用的知识库列表"""
    # 检查应用权限
    app = db.query(App).filter(App.id == app_id).first()
    if not app:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    
    if app.owner_id != current_user.id and not app.is_public:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    knowledge_bases = db.query(KnowledgeBase).filter(
        KnowledgeBase.app_id == app_id
    ).all()
    
    return knowledge_bases


@router.get("/{kb_id}", response_model=KnowledgeBaseResponse)
async def get_knowledge_base(
    kb_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取指定知识库"""
    kb = db.query(KnowledgeBase).filter(KnowledgeBase.id == kb_id).first()
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )
    
    # 检查权限
    app = db.query(App).filter(App.id == kb.app_id).first()
    if app.owner_id != current_user.id and not app.is_public:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )
    
    return kb


@router.delete("/{kb_id}")
async def delete_knowledge_base(
    kb_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除知识库"""
    kb = db.query(KnowledgeBase).filter(KnowledgeBase.id == kb_id).first()
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )

    # 检查权限
    app = db.query(App).filter(App.id == kb.app_id).first()
    if app.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    # 删除知识库
    kb.is_active = False
    db.commit()

    return {"message": "知识库删除成功"}


# 文档管理路由（匹配前端API调用）
@router.post("/{kb_id}/documents", response_model=DocumentResponse)
async def upload_document_to_kb(
    kb_id: int,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """上传文档到知识库（文件上传）"""
    # 检查知识库是否存在且用户有权限
    kb = db.query(KnowledgeBase).filter(KnowledgeBase.id == kb_id).first()
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )

    app = db.query(App).filter(App.id == kb.app_id).first()
    if app.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    # 检查文件类型
    file_extension = file.filename.split('.')[-1].lower()
    if file_extension not in settings.ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的文件类型: {file_extension}"
        )

    # 检查文件大小
    file_content = await file.read()
    if len(file_content) > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="文件大小超过限制"
        )

    # 生成唯一文件名
    file_id = str(uuid.uuid4())
    filename = f"{file_id}.{file_extension}"
    file_path = os.path.join(settings.UPLOAD_PATH, filename)

    # 确保上传目录存在
    os.makedirs(settings.UPLOAD_PATH, exist_ok=True)

    # 保存文件
    with open(file_path, "wb") as f:
        f.write(file_content)

    # 创建文档记录
    db_document = Document(
        filename=filename,
        original_filename=file.filename,
        file_path=file_path,
        file_size=len(file_content),
        file_type=file_extension,
        mime_type=file.content_type,
        knowledge_base_id=kb_id,
        status="pending"
    )

    db.add(db_document)
    db.commit()
    db.refresh(db_document)

    # 异步处理文档（解析、切片、向量化）
    background_tasks.add_task(process_document_async, db_document.id)

    return db_document


@router.post("/{kb_id}/documents/text", response_model=DocumentResponse)
async def upload_text_to_kb(
    kb_id: int,
    doc_data: DocumentUploadRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """上传文本内容到知识库"""
    # 检查知识库是否存在且用户有权限
    kb = db.query(KnowledgeBase).filter(KnowledgeBase.id == kb_id).first()
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )

    app = db.query(App).filter(App.id == kb.app_id).first()
    if app.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    # 生成唯一文件名
    file_id = str(uuid.uuid4())
    filename = f"{file_id}.txt"
    file_path = os.path.join(settings.UPLOAD_PATH, filename)

    # 确保上传目录存在
    os.makedirs(settings.UPLOAD_PATH, exist_ok=True)

    # 保存文本内容到文件
    with open(file_path, "w", encoding="utf-8") as f:
        f.write(doc_data.content)

    # 创建文档记录
    db_document = Document(
        filename=filename,
        original_filename=doc_data.filename,
        file_path=file_path,
        file_size=len(doc_data.content.encode('utf-8')),
        file_type="txt",
        mime_type="text/plain",
        knowledge_base_id=kb_id,
        status="pending",
        content=doc_data.content  # 直接设置内容
    )

    db.add(db_document)
    db.commit()
    db.refresh(db_document)

    # 异步处理文档（切片、向量化）
    background_tasks.add_task(process_document_async, db_document.id)

    return db_document


@router.get("/{kb_id}/documents", response_model=List[DocumentResponse])
async def get_kb_documents(
    kb_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取知识库的文档列表"""
    # 检查权限
    kb = db.query(KnowledgeBase).filter(KnowledgeBase.id == kb_id).first()
    if not kb:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="知识库不存在"
        )

    app = db.query(App).filter(App.id == kb.app_id).first()
    if app.owner_id != current_user.id and not app.is_public:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    documents = db.query(Document).filter(
        Document.knowledge_base_id == kb_id
    ).offset(skip).limit(limit).all()

    return documents


@router.delete("/{kb_id}/documents/{doc_id}")
async def delete_kb_document(
    kb_id: int,
    doc_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """删除知识库中的文档"""
    document = db.query(Document).filter(
        Document.id == doc_id,
        Document.knowledge_base_id == kb_id
    ).first()
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文档不存在"
        )

    # 检查权限
    kb = db.query(KnowledgeBase).filter(KnowledgeBase.id == kb_id).first()
    app = db.query(App).filter(App.id == kb.app_id).first()
    if app.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="权限不足"
        )

    # 从向量数据库删除
    vector_store_manager.delete_document_from_knowledge_base(kb_id, doc_id)

    # 删除文件
    if os.path.exists(document.file_path):
        os.remove(document.file_path)

    # 删除数据库记录
    db.delete(document)
    db.commit()

    return {"message": "文档删除成功"}


async def process_document_async(document_id: int):
    """异步处理文档"""
    from ..core.database import SessionLocal
    from sqlalchemy.orm import Session

    # 创建新的数据库会话
    db: Session = SessionLocal()
    try:
        document = db.query(Document).filter(Document.id == document_id).first()
        if not document:
            print(f"文档不存在: {document_id}")
            return

        print(f"开始处理文档: {document.original_filename} (ID: {document_id})")

        # 更新状态为处理中
        document.status = "processing"
        db.commit()

        try:
            # 如果已有内容，直接使用；否则处理文件
            if document.content:
                content = document.content
                title = document.original_filename
                print(f"使用已有内容，长度: {len(content)}")
            else:
                # 检查文件是否存在
                if not document.file_path or not os.path.exists(document.file_path):
                    raise FileNotFoundError(f"文件不存在: {document.file_path}")

                print(f"处理文件: {document.file_path}")

                # 处理文档内容
                result = document_processor.process_document(
                    document.file_path,
                    document.file_type
                )

                if not result['success']:
                    document.status = "failed"
                    document.error_message = result.get('error', '处理失败')
                    db.commit()
                    print(f"文档处理失败: {result.get('error', '未知错误')}")
                    return

                content = result['content']
                title = result.get('title', document.original_filename)
                document.content = content
                document.title = title
                document.summary = document_processor.extract_summary(content)
                print(f"文档内容提取成功，长度: {len(content)}")

            # 添加到向量数据库
            print(f"开始向量化处理...")
            success = vector_store_manager.add_document_to_knowledge_base(
                document.knowledge_base_id,
                document.id,
                content,
                {
                    'filename': document.original_filename,
                    'file_type': document.file_type,
                    'title': title,
                }
            )

            if success:
                document.status = "completed"
                document.processed_at = datetime.now()
                print(f"文档处理完成: {document.original_filename}")
            else:
                document.status = "failed"
                document.error_message = "向量化失败"
                print(f"向量化失败: {document.original_filename}")

            db.commit()

        except Exception as e:
            print(f"处理文档失败: {e}")
            document.status = "failed"
            document.error_message = str(e)
            db.commit()

    except Exception as e:
        print(f"数据库操作失败: {e}")
    finally:
        db.close()
